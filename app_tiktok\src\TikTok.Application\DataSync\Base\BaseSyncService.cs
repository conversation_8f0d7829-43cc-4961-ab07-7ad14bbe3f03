﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using TikTok.Application.Contracts.MessageProviders;
using TikTok.BusinessApplications;
using TikTok.BusinessCenters;
using TikTok.Consts;
using TikTok.DateTimes;
using TikTokBusinessApi;
using TikTokBusinessApi.Core;
using TikTokBusinessApi.Core.Resilience;
using Volo.Abp;
using Volo.Abp.Uow;

namespace TikTok.DataSync
{
    public abstract class BaseSyncService
    {
        protected readonly ILogger _logger;
        protected readonly IUnitOfWorkManager _unitOfWorkManager;
        protected readonly IBusinessApplicationCache _businessApplicationCache;
        protected readonly IDateTimeService _dateTimeService;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IBusinessCenterCache _businessCenterCache;
        protected readonly IEnumerable<IMessageProvider> _messageProviders;
        protected readonly IConfiguration _configuration;

        // Metrics for API call measurement
        protected readonly Meter _meter;
        protected readonly Counter<long> _apiCallCounter;
        protected readonly Histogram<double> _apiCallDuration;

        protected const int LAST_SYNC_DAYS = 7;
        protected const int PAGE_SIZE_HANDLE_SAVE_TO_DATABASE = 200;
        protected const int PAGE_SIZE_SYNC_DEFAULT = 50;
        protected const int PAGE_SIZE_SYNC_REPORT = 500;

        protected BaseSyncService(IServiceProvider serviceProvider, ILogger logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _unitOfWorkManager = serviceProvider.GetRequiredService<IUnitOfWorkManager>();
            _businessApplicationCache = serviceProvider.GetRequiredService<IBusinessApplicationCache>();
            _dateTimeService = serviceProvider.GetRequiredService<IDateTimeService>();
            _businessCenterCache = serviceProvider.GetRequiredService<IBusinessCenterCache>();
            _configuration = serviceProvider.GetRequiredService<IConfiguration>();
            _messageProviders = serviceProvider.GetRequiredService<IEnumerable<IMessageProvider>>();

            // Initialize metrics
            var serviceName = GetType().Name;
            _meter = new Meter($"TikTok.DataSync.{serviceName}", "1.0.0");
            _apiCallCounter = _meter.CreateCounter<long>(
                name: "tiktok_api_calls_total",
                unit: "calls",
                description: "Total number of TikTok API calls made");
            _apiCallDuration = _meter.CreateHistogram<double>(
                name: "tiktok_api_call_duration_seconds",
                unit: "s",
                description: "Duration of TikTok API calls in seconds");
        }

        /// <summary>
        /// Tạo TikTokBusinessApiClient
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <returns>TikTokBusinessApiClient</returns>
        /// <exception cref="BusinessException">Nếu không tìm thấy Business Application hoặc Access token không hợp lệ</exception>
        protected virtual async Task<TikTokBusinessApiClient> CreateTikTokBusinessApiClient(string bcId)
        {
            // 1. Lấy cấu hình ứng dụng từ BC ID
            var activeApplication = await GetApplicationActive(bcId);

            if (string.IsNullOrEmpty(activeApplication.AccessToken))
            {
                throw new BusinessException(TikTokApiCodes.AccessTokenExpired.ToString(), $"Access token không hợp lệ cho ứng dụng: {activeApplication.ApplicationId}");
            }

            // 2. Tạo TikTok client
            var client =  new TikTokBusinessApiClient(Configuration.CreateForProduction(activeApplication.ApplicationId, activeApplication.AccessToken));
            client.ApiClient.RateLimiter = new DefaultRateLimiter(
                new RateLimiterOptions
                {
                    RequestsPerWindow = 8, // Số lượng request tối đa trong một khoảng thời gian
                    WindowDuration = TimeSpan.FromSeconds(1), // Khoảng thời gian tính rate limit
                    UseSlidingWindow = true, // Sử dụng sliding window
                    MaxWaitTime = TimeSpan.FromSeconds(5) // Thời gian tối đa chờ đợi khi rate limit bị vượt
                });
            return client;
        }
        /// <summary>
        /// Tạo TikTokBusinessApiClient với cấu hình rate limit
        /// </summary>
        /// <param name="bcId"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        protected virtual async Task<TikTokBusinessApiClient> CreateTikTokBusinessApiClientWithOption(string bcId, RateLimiterOptions rateLimitOption = null, RetryPolicyOptions retryPolicyOption = null)
        {
            // 1. Lấy cấu hình ứng dụng từ BC ID
            var activeApplication = await GetApplicationActive(bcId);

            if (string.IsNullOrEmpty(activeApplication.AccessToken))
            {
                throw new BusinessException(TikTokApiCodes.AccessTokenExpired.ToString(), $"Access token không hợp lệ cho ứng dụng: {activeApplication.ApplicationId}");
            }

            // 2. Tạo TikTok client
            var client = new TikTokBusinessApiClient(
                Configuration.CreateForProduction(activeApplication.ApplicationId, activeApplication.AccessToken));
            if(rateLimitOption != null) client.ApiClient.RateLimiter = new DefaultRateLimiter(rateLimitOption);
            if(retryPolicyOption != null) client.ApiClient.RetryPolicy = new DefaultRetryPolicy(retryPolicyOption);
            return client;
        }
        /// <summary>
        /// Lấy Business Application active
        /// </summary>
        /// <param name="bcId">Business Center ID</param>
        /// <returns>Business Application</returns>
        /// <exception cref="BusinessException">Nếu không tìm thấy Business Application</exception>
        protected virtual async Task<BusinessApplicationDto> GetApplicationActive(string bcId)
        {
            // 1. Lấy cấu hình ứng dụng từ BC ID
            var activeApplication = await _businessApplicationCache.GetByBcIdAsync(bcId);
            if (activeApplication == null)
            {
                throw new BusinessException(TikTokApiCodes.ObjectNotExists.ToString(), "Business Application Not Found");
            }

            return activeApplication;
        }

        /// <summary>
        /// Lấy cấu hình RateLimit từ appsettings
        /// </summary>
        /// <returns>RateLimiterOptions</returns>
        protected virtual RateLimiterOptions GetRateLimitOptions()
        {
            var maxWaitTime = _configuration.GetValue<int>("APIClient:RateLimit:MaxWaitTime", 30);
            var requestsPerWindow = _configuration.GetValue<int>("APIClient:RateLimit:RequestsPerWindow", 1);
            var windowDuration = _configuration.GetValue<int>("APIClient:RateLimit:WindowDuration", 2);
            var useSlidingWindow = _configuration.GetValue<bool>("APIClient:RateLimit:UseSlidingWindow", true);

            return new RateLimiterOptions
            {
                MaxWaitTime = TimeSpan.FromSeconds(maxWaitTime),
                RequestsPerWindow = requestsPerWindow,
                WindowDuration = TimeSpan.FromSeconds(windowDuration),
                UseSlidingWindow = useSlidingWindow
            };
        }

        protected virtual RetryPolicyOptions GetRetryPolicy()
        {
            var baseDelay = _configuration.GetValue<int>("APIClient:Retry:BaseDelay", 1);
            var maxDelay = _configuration.GetValue<int>("APIClient:Retry:MaxDelay", 30);
            var maxRetryAttempts = _configuration.GetValue<int>("APIClient:Retry:MaxRetryAttempts", 3);
            var useExponentialBackoff = _configuration.GetValue<bool>("APIClient:Retry:UseExponentialBackoff", true);
            var jitterFactor = _configuration.GetValue<double>("APIClient:Retry:JitterFactor", 0.1);
            return new RetryPolicyOptions
            {
                BaseDelay = TimeSpan.FromSeconds(baseDelay),
                MaxDelay = TimeSpan.FromSeconds(maxDelay),
                MaxRetryAttempts = maxRetryAttempts,
                UseExponentialBackoff = useExponentialBackoff,
                JitterFactor = jitterFactor
            };
        }
        protected virtual async Task SendToTelegram(List<string> unauthorizedAdvertiserIds)
        {
            if (unauthorizedAdvertiserIds.Count > 0)
            {
                // Send tele
                if (_messageProviders.Any())
                {
                    // Get TelegramMessageProvider from _messageProviders
                    var telegramProvider = _messageProviders.FirstOrDefault(p =>
                        p.Name.Equals("Telegram", StringComparison.OrdinalIgnoreCase));

                    if (telegramProvider != null && telegramProvider.IsEnabled)
                    {
                        var message = string.Join('\n', unauthorizedAdvertiserIds);
                        _logger.LogDebug("Sending Telegram message: {Message}", message);
                        await telegramProvider.SendMessageByChatIdAsync(message, _configuration.GetValue<string>("MessageProviderOption:Telegram:AdAccountChatId", "-*************"));

                    }
                }
            }
        }
        /// <summary>
        /// Lấy giá trị decimal từ metrics
        /// </summary>
        /// <param name="metrics">Dictionary chứa metrics</param>
        /// <param name="key">Key cần lấy</param>
        /// <returns>Giá trị decimal</returns>
        protected decimal GetDecimalValue(Dictionary<string, object>? metrics, string key)
        {
            if (metrics?.ContainsKey(key) == true && metrics[key] != null)
            {
                if (decimal.TryParse(metrics[key].ToString(),NumberStyles.Any, CultureInfo.InvariantCulture,  out var value))
                {
                    return value;
                }
            }
            return 0;
        }

        /// <summary>
        /// Lấy giá trị long từ metrics
        /// </summary>
        /// <param name="metrics">Dictionary chứa metrics</param>
        /// <param name="key">Key cần lấy</param>
        /// <returns>Giá trị long</returns>
        protected long GetLongValue(Dictionary<string, object>? metrics, string key)
        {
            if (metrics?.ContainsKey(key) == true && metrics[key] != null)
            {
                if (long.TryParse(metrics[key].ToString(), out var value))
                {
                    return value;
                }
            }
            return 0;
        }

        /// <summary>
        /// Lấy giá trị string từ metrics
        /// </summary>
        /// <param name="metrics">Dictionary chứa metrics</param>
        /// <param name="key">Key cần lấy</param>
        /// <returns>Giá trị string</returns>
        protected string? GetStringValue(Dictionary<string, object>? metrics, string key)
        {
            if (metrics?.ContainsKey(key) == true && metrics[key] != null)
            {
                return metrics[key].ToString();
            }
            return null;
        }

        /// <summary>
        /// Lấy giá trị int từ metrics
        /// </summary>
        /// <param name="metrics">Dictionary chứa metrics</param>
        /// <param name="key">Key cần lấy</param>
        /// <returns>Giá trị int</returns>
        protected int? GetIntValue(Dictionary<string, object>? metrics, string key)
        {
            if (metrics?.ContainsKey(key) == true && metrics[key] != null)
            {
                return int.TryParse(metrics[key].ToString(), out var value) ? value : null;
            }
            return null;
        }

        /// <summary>
        /// Parse DateTime từ string
        /// </summary>
        protected DateTime? ParseDateTime(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (DateTime.TryParse(value, out var result))
                return result;

            return null;
        }

        /// <summary>
        /// Đo lường API call và thực thi
        /// </summary>
        /// <typeparam name="T">Kiểu trả về của API call</typeparam>
        /// <param name="apiCall">Hàm API call cần đo lường</param>
        /// <param name="apiName">Tên API để ghi log</param>
        /// <param name="tags">Tags bổ sung cho metrics</param>
        /// <returns>Kết quả của API call</returns>
        protected async Task<T> MeasureApiCallAsync<T>(Func<Task<T>> apiCall, string apiName, params (string Key, object? Value)[] tags)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var allTags = new List<KeyValuePair<string, object?>>
            {
                new("api_name", apiName),
                new("service", GetType().Name)
            };

            if (tags != null)
            {
                allTags.AddRange(tags.Select(t => new KeyValuePair<string, object?>(t.Key, t.Value)));
            }

            // Record API call immediately (regardless of success/failure)
            _apiCallCounter.Add(1, allTags.ToArray());

            try
            {
                var result = await apiCall();
                stopwatch.Stop();

                // Record duration for successful API call
                _apiCallDuration.Record(stopwatch.Elapsed.TotalSeconds, allTags.Append(new KeyValuePair<string, object?>("status", "success")).ToArray());

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Record duration for failed API call
                _apiCallDuration.Record(stopwatch.Elapsed.TotalSeconds, allTags.Append(new KeyValuePair<string, object?>("status", "error")).Append(new KeyValuePair<string, object?>("error_type", ex.GetType().Name)).ToArray());

                throw;
            }
        }

        /// <summary>
        /// Log metrics summary khi job hoàn thành
        /// </summary>
        /// <param name="jobName">Tên job</param>
        /// <param name="startTime">Thời gian bắt đầu job</param>
        /// <param name="totalApiCalls">Tổng số API calls đã thực hiện</param>
        /// <param name="additionalMetrics">Metrics bổ sung</param>
        protected void LogJobMetrics(string jobName, DateTime startTime, long totalApiCalls, Dictionary<string, object>? additionalMetrics = null)
        {
            var duration = DateTime.UtcNow - startTime;
            var apiCallsPerSecond = duration.TotalSeconds > 0 ? totalApiCalls / duration.TotalSeconds : 0;
            var apiCallsPerMinute = duration.TotalMinutes > 0 ? totalApiCalls / duration.TotalMinutes : 0;

            var logMessage = "Job {JobName} completed. Duration: {Duration:hh\\:mm\\:ss}, " +
                           "Total API calls: {TotalApiCalls}, " +
                           "API calls/second: {ApiCallsPerSecond:F2}, " +
                           "API calls/minute: {ApiCallsPerMinute:F2}";

            var logArgs = new List<object> { jobName, duration, totalApiCalls, apiCallsPerSecond, apiCallsPerMinute };

            if (additionalMetrics != null && additionalMetrics.Any())
            {
                foreach (var metric in additionalMetrics)
                {
                    logMessage += $", {metric.Key}: {{{metric.Key}}}";
                    logArgs.Add(metric.Value);
                }
            }

            _logger.LogInformation(logMessage, logArgs.ToArray());
        }

        /// <summary>
        /// Parse DateTime từ The time when the ad account was created, in the format of an Epoch/Unix timestamp in seconds.
        ///  Example: **********.
        /// </summary>
        /// <param name="value">Giá trị string</param>
        /// <returns>DateTime hoặc null</returns>
        protected DateTime? ParseDateTime(long? value)
        {
            if (value == null)
                return null;

            return DateTimeOffset.FromUnixTimeSeconds(value.Value).DateTime;
        }
    }
}