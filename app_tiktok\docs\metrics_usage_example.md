# System.Diagnostics.Metrics Usage in TikTok Sync Services

## Overview

Đã thêm System.Diagnostics.Metrics vào các sync service để đo lường số lần gọi API trên giây và phút. Metrics sẽ được log ra khi job chạy xong.

## Implementation Details

### BaseSyncService Changes

1. **Added Metrics Infrastructure**:

    ```csharp
    // Metrics for API call measurement
    protected readonly Meter _meter;
    protected readonly Counter<long> _apiCallCounter;
    protected readonly Histogram<double> _apiCallDuration;
    ```

2. **Metrics Initialization**:

    ```csharp
    // Initialize metrics
    var serviceName = GetType().Name;
    _meter = new Meter($"TikTok.DataSync.{serviceName}", "1.0.0");
    _apiCallCounter = _meter.CreateCounter<long>(
        name: "tiktok_api_calls_total",
        unit: "calls",
        description: "Total number of TikTok API calls made");
    _apiCallDuration = _meter.CreateHistogram<double>(
        name: "tiktok_api_call_duration_seconds",
        unit: "s",
        description: "Duration of TikTok API calls in seconds");
    ```

3. **API Call Measurement Method**:

    ```csharp
    protected async Task<T> MeasureApiCallAsync<T>(
        Func<Task<T>> apiCall,
        string apiName,
        params (string Key, object? Value)[] tags)
    {
        // Record API call immediately (regardless of success/failure)
        _apiCallCounter.Add(1, allTags.ToArray());
        // ... measurement logic
    }
    ```

4. **Job Metrics Logging**:
    ```csharp
    protected void LogJobMetrics(
        string jobName,
        DateTime startTime,
        long totalApiCalls,
        Dictionary<string, object>? additionalMetrics = null)
    {
        var duration = DateTime.UtcNow - startTime;
        var apiCallsPerSecond = duration.TotalSeconds > 0 ? totalApiCalls / duration.TotalSeconds : 0;
        var apiCallsPerMinute = duration.TotalMinutes > 0 ? totalApiCalls / duration.TotalMinutes : 0;
        // ... logging logic
    }
    ```

### Service-Specific Changes

#### GmvMaxLiveCampaignSyncService

-   Wrapped API calls with `MeasureApiCallAsync`
-   Added API call counting
-   Log metrics at job completion

#### GmvMaxProductCampaignSyncService

-   Similar implementation to Live Campaign service
-   Tracks API calls per store and date range

#### GmvMaxProductCreativeSyncService

-   Tracks API calls per item group
-   More granular metrics due to creative-level data

## Usage Example

### API Call Measurement

```csharp
var response = await MeasureApiCallAsync(
    () => tikTokClient.GMVMax.GetReportAsync(request),
    "GMVMax.GetReport",
    ("advertiser_id", advertiserId),
    ("store_id", storeId),
    ("report_type", "live_campaign"),
    ("page", page)
);
```

### Job Metrics Logging

```csharp
LogJobMetrics("SyncAllGmvMaxLiveCampaignForAllBcs", startTime, totalApiCalls, new Dictionary<string, object>
{
    ["TotalSynced"] = result.TotalSynced,
    ["NewRecords"] = result.NewRecords,
    ["UpdatedRecords"] = result.UpdatedRecords,
    ["BcCount"] = result.BcCount,
    ["CampaignCount"] = result.CampaignCount,
    ["StoreCount"] = result.StoreCount
});
```

## Expected Log Output

```
Job SyncAllGmvMaxLiveCampaignForAllBcs completed. Duration: 00:05:30, Total API calls: 150, API calls/second: 0.45, API calls/minute: 27.27, TotalSynced: 1250, NewRecords: 800, UpdatedRecords: 450, BcCount: 5, CampaignCount: 25, StoreCount: 10
```

## Metrics Tags

### API Call Counter Tags

Each API call count is tagged with:

-   `api_name`: Name of the API method (e.g., "GMVMax.GetReport")
-   `service`: Name of the sync service
-   `advertiser_id`: Advertiser ID
-   `store_id`: Store ID (when applicable)
-   `report_type`: Type of report being synced
-   `page`: Page number for paginated requests

### API Call Duration Tags

Duration metrics include additional tags:

-   All counter tags above, plus:
-   `status`: "success" or "error"
-   `error_type`: Exception type name (for failed calls only)

## Key Features

### Total API Call Counting

-   **All API calls are counted**: Bất kể thành công hay thất bại, tất cả API calls đều được đếm
-   **Immediate counting**: API call được đếm ngay khi bắt đầu thực hiện, không chờ kết quả
-   **Accurate metrics**: Đảm bảo số liệu chính xác về tổng số lần gọi API

### Duration Tracking

-   **Success/Failure differentiation**: Duration metrics có thêm tag để phân biệt success/error
-   **Error details**: Failed calls có thêm error_type tag để debug

## Benefits

1. **Performance Monitoring**: Track API call rates and duration
2. **Rate Limit Management**: Monitor API usage patterns (bao gồm cả failed calls)
3. **Debugging**: Identify slow or failing API calls
4. **Capacity Planning**: Understand API usage trends (total calls, not just successful ones)
5. **SLA Monitoring**: Track job completion times and success rates

## Future Enhancements

1. Export metrics to monitoring systems (Prometheus, Application Insights)
2. Add alerting based on metrics thresholds
3. Create dashboards for real-time monitoring
4. Add more granular metrics (per-endpoint, per-BC)
